<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Offline</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #1e1e1e;
      color: #e0e0e0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
    }
    .container {
      max-width: 90%;
      text-align: center;
      padding: 20px;
    }
    h1 {
      font-size: 2rem;
      color: #f7fafc;
      margin-bottom: 0.5em;
    }
    p {
      font-size: 1rem;
      color: #a0aec0;
      margin-bottom: 1.5em;
    }
    button {
      padding: 12px 20px;
      font-size: 1rem;
      border: none;
      border-radius: 8px;
      background-color: #2b6cb0;
      color: white;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }
    button:hover {
      background-color: #2c5282;
    }
    .icon {
      width: 120px;
      margin-bottom: 2em;
      margin: 0 auto;
    }
    svg {
      width: 100%;
      height: auto;
      fill: #a0aec0;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="icon">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
        <path d="M32 4C17.664 4 4.946 13.107 0 26h5.693c4.506-10.186 14.857-17.184 26.307-17.184 11.45 0 21.801 6.998 26.307 17.184H64C59.054 13.107 46.336 4 32 4zm0 12a22 22 0 0 0-20.662 14h41.324A22 22 0 0 0 32 16zm0 12a10 10 0 0 0-9.489 7h18.978A10 10 0 0 0 32 28zm0 10a4 4 0 1 0 0 8 4 4 0 0 0 0-8z"/>
      </svg>
    </div>
    <h1>No Internet</h1>
    <p>Please check your connection and try again.</p>
    <button onclick="location.href='https://tech-news.io'">Retry</button>
  </div>
</body>
</html>
