# Tech News App

A dedicated mobile client for tech-news.io - your source for the latest technology news and insights.

## Features

- **Exclusive tech-news.io Access**: Navigate only within the tech-news.io domain for focused news consumption
- **Enhanced Loading Experience**: Progress indicators and loading overlays for smooth user experience
- **Navigation Controls**: Back, forward, refresh, and home buttons for easy navigation
- **Pull-to-Refresh**: Simple gesture to refresh content
- **Error Handling**: Comprehensive error handling with retry functionality
- **Responsive Design**: Optimized for mobile devices with clean, professional UI

## Tech Stack

- **Flutter**: Cross-platform mobile development framework
- **WebView Flutter**: Native WebView integration for seamless web content display
- **Material Design**: Modern UI components following Material Design principles

## Getting Started

### Prerequisites
- Flutter SDK (3.8.1 or higher)
- Dart SDK
- Android Studio / VS Code with Flutter extensions

### Installation
1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Run `flutter run` to start the application

### Supported Platforms
- Android
- iOS
- Web
- macOS
- Windows
- Linux

## Security Features

- **Domain Restriction**: Navigation is restricted to tech-news.io and its subdomains only
- **Secure WebView Configuration**: Optimized security settings for safe browsing
- **Custom User Agent**: Identifies as official tech-news.io mobile client
