import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/firebase_service.dart';
import '../services/permission_service.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  final FirebaseService _firebaseService = FirebaseService();
  
  // Notification preferences
  bool _breakingNews = true;
  bool _dailyDigest = true;
  bool _trendingArticles = true;
  bool _savedUpdates = true;
  bool _quietHoursEnabled = false;
  
  // Quiet hours
  TimeOfDay _quietHoursStart = const TimeOfDay(hour: 22, minute: 0);
  TimeOfDay _quietHoursEnd = const TimeOfDay(hour: 7, minute: 0);
  
  // Topics
  final Map<String, bool> _topics = {
    'artificial-intelligence': false,
    'startups': false,
    'cryptocurrency': false,
    'mobile-development': false,
    'web-development': false,
    'cybersecurity': false,
    'cloud-computing': false,
    'gaming': false,
    'hardware': false,
    'science': false,
  };
  
  NotificationPermissionStatus _permissionStatus = NotificationPermissionStatus.notDetermined;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Load permission status
    _permissionStatus = await _firebaseService.getPermissionStatus();
    
    setState(() {
      // Load notification preferences
      _breakingNews = prefs.getBool('pref_breaking_news') ?? true;
      _dailyDigest = prefs.getBool('pref_daily_digest') ?? true;
      _trendingArticles = prefs.getBool('pref_trending_articles') ?? true;
      _savedUpdates = prefs.getBool('pref_saved_updates') ?? true;
      _quietHoursEnabled = prefs.getBool('pref_quiet_hours_enabled') ?? false;
      
      // Load quiet hours
      final startTime = prefs.getString('pref_quiet_hours_start') ?? '22:00';
      final endTime = prefs.getString('pref_quiet_hours_end') ?? '07:00';
      _quietHoursStart = _parseTimeOfDay(startTime);
      _quietHoursEnd = _parseTimeOfDay(endTime);
      
      // Load topics
      final savedTopics = prefs.getStringList('pref_topics') ?? [];
      for (final topic in _topics.keys) {
        _topics[topic] = savedTopics.contains(topic);
      }
      
      _isLoading = false;
    });
  }

  TimeOfDay _parseTimeOfDay(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
  }

  String _formatTimeOfDay(TimeOfDay time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Save notification preferences
    await prefs.setBool('pref_breaking_news', _breakingNews);
    await prefs.setBool('pref_daily_digest', _dailyDigest);
    await prefs.setBool('pref_trending_articles', _trendingArticles);
    await prefs.setBool('pref_saved_updates', _savedUpdates);
    await prefs.setBool('pref_quiet_hours_enabled', _quietHoursEnabled);
    
    // Save quiet hours
    await prefs.setString('pref_quiet_hours_start', _formatTimeOfDay(_quietHoursStart));
    await prefs.setString('pref_quiet_hours_end', _formatTimeOfDay(_quietHoursEnd));
    
    // Save topics
    final selectedTopics = _topics.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();
    await prefs.setStringList('pref_topics', selectedTopics);
    
    // Update topic subscriptions
    await _updateTopicSubscriptions();
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white, size: 20),
              SizedBox(width: 8),
              Text('Settings saved successfully'),
            ],
          ),
          backgroundColor: Color(0xFF1E3A8A),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _updateTopicSubscriptions() async {
    for (final entry in _topics.entries) {
      if (entry.value) {
        await _firebaseService.subscribeToTopic(entry.key);
      } else {
        await _firebaseService.unsubscribeFromTopic(entry.key);
      }
    }
  }

  Future<void> _requestPermissions() async {
    final status = await _firebaseService.requestPermission(context);
    setState(() {
      _permissionStatus = status;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Notification Settings'),
          backgroundColor: const Color(0xFF1E3A8A),
          foregroundColor: Colors.white,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Settings'),
        backgroundColor: const Color(0xFF1E3A8A),
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _saveSettings,
            child: const Text(
              'Save',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildPermissionSection(),
          const SizedBox(height: 24),
          _buildNotificationTypesSection(),
          const SizedBox(height: 24),
          _buildTopicsSection(),
          const SizedBox(height: 24),
          _buildQuietHoursSection(),
          const SizedBox(height: 24),
          _buildAdvancedSection(),
        ],
      ),
    );
  }

  Widget _buildPermissionSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.security, color: Color(0xFF1E3A8A)),
                SizedBox(width: 8),
                Text(
                  'Notification Permissions',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildPermissionStatus(),
            if (_permissionStatus != NotificationPermissionStatus.granted) ...[
              const SizedBox(height: 12),
              ElevatedButton.icon(
                onPressed: _requestPermissions,
                icon: const Icon(Icons.notifications_active),
                label: const Text('Enable Notifications'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF1E3A8A),
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionStatus() {
    IconData icon;
    String text;
    Color color;
    
    switch (_permissionStatus) {
      case NotificationPermissionStatus.granted:
        icon = Icons.check_circle;
        text = 'Notifications are enabled';
        color = Colors.green;
        break;
      case NotificationPermissionStatus.denied:
        icon = Icons.block;
        text = 'Notifications are disabled';
        color = Colors.red;
        break;
      case NotificationPermissionStatus.provisional:
        icon = Icons.info;
        text = 'Notifications are partially enabled';
        color = Colors.orange;
        break;
      case NotificationPermissionStatus.notDetermined:
        icon = Icons.help_outline;
        text = 'Notification permission not set';
        color = Colors.grey;
        break;
    }
    
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 8),
        Text(text, style: TextStyle(color: color)),
      ],
    );
  }

  Widget _buildNotificationTypesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.notifications, color: Color(0xFF1E3A8A)),
                SizedBox(width: 8),
                Text(
                  'Notification Types',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildNotificationToggle(
              'Breaking News',
              'Urgent tech news and breaking stories',
              Icons.flash_on,
              _breakingNews,
              (value) => setState(() => _breakingNews = value),
            ),
            _buildNotificationToggle(
              'Daily Digest',
              'Daily summary of top tech stories',
              Icons.schedule,
              _dailyDigest,
              (value) => setState(() => _dailyDigest = value),
            ),
            _buildNotificationToggle(
              'Trending Articles',
              'Popular articles and viral tech content',
              Icons.trending_up,
              _trendingArticles,
              (value) => setState(() => _trendingArticles = value),
            ),
            _buildNotificationToggle(
              'Saved Article Updates',
              'Updates on articles you\'ve bookmarked',
              Icons.bookmark,
              _savedUpdates,
              (value) => setState(() => _savedUpdates = value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationToggle(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return ListTile(
      leading: Icon(icon, color: const Color(0xFF1E3A8A)),
      title: Text(title),
      subtitle: Text(subtitle, style: const TextStyle(fontSize: 12)),
      trailing: Switch(
        value: value,
        onChanged: _permissionStatus == NotificationPermissionStatus.granted
            ? onChanged
            : null,
        activeColor: const Color(0xFF1E3A8A),
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildTopicsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.topic, color: Color(0xFF1E3A8A)),
                SizedBox(width: 8),
                Text(
                  'Topics of Interest',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'Get notifications about topics you care about',
              style: TextStyle(color: Colors.grey, fontSize: 14),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _topics.entries.map((entry) {
                return FilterChip(
                  label: Text(_getTopicDisplayName(entry.key)),
                  selected: entry.value,
                  onSelected: _permissionStatus == NotificationPermissionStatus.granted
                      ? (selected) {
                          setState(() {
                            _topics[entry.key] = selected;
                          });
                        }
                      : null,
                  selectedColor: const Color(0xFF1E3A8A).withValues(alpha: 0.2),
                  checkmarkColor: const Color(0xFF1E3A8A),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  String _getTopicDisplayName(String topic) {
    switch (topic) {
      case 'artificial-intelligence':
        return 'AI & Machine Learning';
      case 'startups':
        return 'Startups';
      case 'cryptocurrency':
        return 'Cryptocurrency';
      case 'mobile-development':
        return 'Mobile Development';
      case 'web-development':
        return 'Web Development';
      case 'cybersecurity':
        return 'Cybersecurity';
      case 'cloud-computing':
        return 'Cloud Computing';
      case 'gaming':
        return 'Gaming';
      case 'hardware':
        return 'Hardware';
      case 'science':
        return 'Science';
      default:
        return topic;
    }
  }

  Widget _buildQuietHoursSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.bedtime, color: Color(0xFF1E3A8A)),
                SizedBox(width: 8),
                Text(
                  'Quiet Hours',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'Suppress notifications during these hours',
              style: TextStyle(color: Colors.grey, fontSize: 14),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Enable Quiet Hours'),
              value: _quietHoursEnabled,
              onChanged: _permissionStatus == NotificationPermissionStatus.granted
                  ? (value) => setState(() => _quietHoursEnabled = value)
                  : null,
              activeColor: const Color(0xFF1E3A8A),
              contentPadding: EdgeInsets.zero,
            ),
            if (_quietHoursEnabled) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildTimeSelector(
                      'Start Time',
                      _quietHoursStart,
                      (time) => setState(() => _quietHoursStart = time),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildTimeSelector(
                      'End Time',
                      _quietHoursEnd,
                      (time) => setState(() => _quietHoursEnd = time),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTimeSelector(String label, TimeOfDay time, ValueChanged<TimeOfDay> onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        InkWell(
          onTap: () async {
            final selectedTime = await showTimePicker(
              context: context,
              initialTime: time,
            );
            if (selectedTime != null) {
              onChanged(selectedTime);
            }
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(time.format(context)),
                const Icon(Icons.access_time, size: 20),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAdvancedSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.settings, color: Color(0xFF1E3A8A)),
                SizedBox(width: 8),
                Text(
                  'Advanced',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.history, color: Color(0xFF1E3A8A)),
              title: const Text('Notification History'),
              subtitle: const Text('View past notifications'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () => _showNotificationHistory(),
              contentPadding: EdgeInsets.zero,
            ),
            ListTile(
              leading: const Icon(Icons.info, color: Color(0xFF1E3A8A)),
              title: const Text('Token Information'),
              subtitle: const Text('View FCM token details'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () => _showTokenInfo(),
              contentPadding: EdgeInsets.zero,
            ),
            ListTile(
              leading: const Icon(Icons.refresh, color: Color(0xFF1E3A8A)),
              title: const Text('Refresh Token'),
              subtitle: const Text('Force refresh FCM token'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () => _refreshToken(),
              contentPadding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
    );
  }

  void _showNotificationHistory() {
    final history = _firebaseService.getNotificationHistory();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Notification History'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: history.isEmpty
              ? const Center(child: Text('No notifications yet'))
              : ListView.builder(
                  itemCount: history.length,
                  itemBuilder: (context, index) {
                    final notification = history[index];
                    return ListTile(
                      title: Text(notification.title),
                      subtitle: Text(notification.body),
                      trailing: Text(
                        '${notification.timestamp.hour}:${notification.timestamp.minute.toString().padLeft(2, '0')}',
                        style: const TextStyle(fontSize: 12),
                      ),
                    );
                  },
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          if (history.isNotEmpty)
            TextButton(
              onPressed: () async {
                final navigator = Navigator.of(context);
                final messenger = ScaffoldMessenger.of(context);
                await _firebaseService.clearNotificationHistory();
                if (mounted) {
                  navigator.pop();
                  messenger.showSnackBar(
                    const SnackBar(content: Text('History cleared')),
                  );
                }
              },
              child: const Text('Clear'),
            ),
        ],
      ),
    );
  }

  void _showTokenInfo() async {
    final tokenStats = await _firebaseService.getTokenStats();

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Token Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('Has Token', tokenStats['has_token'].toString()),
            _buildInfoRow('Token Age', '${tokenStats['token_age_days']} days'),
            _buildInfoRow('Is Synced', tokenStats['is_synced'].toString()),
            _buildInfoRow('Platform', tokenStats['platform']),
            const SizedBox(height: 8),
            const Text('Token:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                _firebaseService.fcmToken ?? 'No token',
                style: const TextStyle(fontSize: 10, fontFamily: 'monospace'),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  void _refreshToken() async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('Refreshing token...'),
          ],
        ),
      ),
    );

    final newToken = await _firebaseService.forceRefreshToken();

    if (!mounted) return;

    Navigator.pop(context); // Close loading dialog

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          newToken != null
              ? 'Token refreshed successfully'
              : 'Failed to refresh token',
        ),
        backgroundColor: newToken != null ? Colors.green : Colors.red,
      ),
    );
  }
}
