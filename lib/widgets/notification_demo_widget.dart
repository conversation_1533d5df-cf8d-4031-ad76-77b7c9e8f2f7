import 'package:flutter/material.dart';
import '../services/firebase_service.dart';
import '../services/notification_templates.dart';
import '../services/notification_handler.dart';

class NotificationDemoWidget extends StatelessWidget {
  const NotificationDemoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.notifications_active, color: Color(0xFF1E3A8A)),
                SizedBox(width: 8),
                Text(
                  'Push Notifications Demo',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Test different notification types:',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            _buildDemoButton(
              context,
              'Breaking News',
              'Test urgent breaking news notification',
              Icons.flash_on,
              Colors.red,
              () => _sendTestNotification(context, NotificationType.breakingNews),
            ),
            const SizedBox(height: 8),
            _buildDemoButton(
              context,
              'Daily Digest',
              'Test daily news summary notification',
              Icons.schedule,
              Colors.blue,
              () => _sendTestNotification(context, NotificationType.dailyDigest),
            ),
            const SizedBox(height: 8),
            _buildDemoButton(
              context,
              'Trending Article',
              'Test trending article notification',
              Icons.trending_up,
              Colors.green,
              () => _sendTestNotification(context, NotificationType.trendingArticle),
            ),
            const SizedBox(height: 8),
            _buildDemoButton(
              context,
              'General',
              'Test general notification',
              Icons.info,
              Colors.grey,
              () => _sendTestNotification(context, NotificationType.general),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDemoButton(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color.withValues(alpha: 0.1),
          foregroundColor: color,
          elevation: 0,
          padding: const EdgeInsets.all(12),
        ),
        child: Row(
          children: [
            Icon(icon, color: color),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: color.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _sendTestNotification(BuildContext context, NotificationType type) {
    // Create test notification data
    final testData = _getTestDataForType(type);
    
    // Show local notification using the notification handler
    final notificationHandler = NotificationHandler();
    
    // Create a mock RemoteMessage-like data structure
    final notificationData = NotificationData(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: type,
      title: testData['title']!,
      body: testData['body']!,
      imageUrl: testData['imageUrl'],
      articleUrl: testData['articleUrl'],
      data: testData,
      timestamp: DateTime.now(),
    );

    // Show the notification
    notificationHandler.handleNotification(
      // This would normally be a RemoteMessage, but we're creating a mock for demo
      _createMockRemoteMessage(testData),
    );

    // Show feedback to user
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text('${testData['title']} notification sent!'),
          ],
        ),
        backgroundColor: const Color(0xFF1E3A8A),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Map<String, String> _getTestDataForType(NotificationType type) {
    switch (type) {
      case NotificationType.breakingNews:
        return {
          'title': 'BREAKING: Major Tech Acquisition',
          'body': 'Tech giant announces \$50B acquisition of AI startup, reshaping the industry landscape.',
          'type': 'breaking_news',
          'articleUrl': 'https://tech-news.io/breaking/major-acquisition',
          'imageUrl': 'https://example.com/breaking-news.jpg',
        };
      
      case NotificationType.dailyDigest:
        return {
          'title': 'Your Daily Tech Digest',
          'body': '5 top stories today: AI breakthrough, startup funding, new iPhone rumors, and more.',
          'type': 'daily_digest',
          'articles': '["AI Breakthrough", "Startup Funding", "iPhone Rumors", "Cloud Security", "Gaming News"]',
        };
      
      case NotificationType.trendingArticle:
        return {
          'title': 'The Future of Quantum Computing',
          'body': 'New quantum processor achieves unprecedented performance in breakthrough experiment.',
          'type': 'trending_article',
          'articleUrl': 'https://tech-news.io/quantum-computing-future',
          'imageUrl': 'https://example.com/quantum.jpg',
          'author': 'Dr. Sarah Chen',
          'read_time': '8',
        };
      
      case NotificationType.general:
        return {
          'title': 'Welcome to Tech News!',
          'body': 'Stay updated with the latest technology news and insights.',
          'type': 'general',
        };
      
      default:
        return {
          'title': 'Test Notification',
          'body': 'This is a test notification from Tech News app.',
          'type': 'general',
        };
    }
  }

  // Mock RemoteMessage for demo purposes
  dynamic _createMockRemoteMessage(Map<String, String> data) {
    // In a real implementation, this would be a proper RemoteMessage
    // For demo purposes, we'll create a simple object that our handler can process
    return MockRemoteMessage(data);
  }
}

// Mock class for demo purposes
class MockRemoteMessage {
  final Map<String, String> data;
  final MockNotification? notification;
  final String? messageId;

  MockRemoteMessage(this.data)
      : notification = MockNotification(
          title: data['title'],
          body: data['body'],
        ),
        messageId = DateTime.now().millisecondsSinceEpoch.toString();
}

class MockNotification {
  final String? title;
  final String? body;

  MockNotification({this.title, this.body});
}
