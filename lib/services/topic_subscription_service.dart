import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class TopicSubscriptionService {
  static final TopicSubscriptionService _instance = TopicSubscriptionService._internal();
  factory TopicSubscriptionService() => _instance;
  TopicSubscriptionService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  
  // Available topics with metadata
  static const Map<String, TopicInfo> availableTopics = {
    'artificial-intelligence': TopicInfo(
      id: 'artificial-intelligence',
      displayName: 'AI & Machine Learning',
      description: 'Latest developments in artificial intelligence, machine learning, and neural networks',
      icon: Icons.psychology,
      category: TopicCategory.technology,
    ),
    'startups': TopicInfo(
      id: 'startups',
      displayName: 'Startups',
      description: 'Startup news, funding rounds, and entrepreneurship stories',
      icon: Icons.rocket_launch,
      category: TopicCategory.business,
    ),
    'cryptocurrency': TopicInfo(
      id: 'cryptocurrency',
      displayName: 'Cryptocurrency',
      description: 'Bitcoin, Ethereum, DeFi, and blockchain technology news',
      icon: Icons.currency_bitcoin,
      category: TopicCategory.finance,
    ),
    'mobile-development': TopicInfo(
      id: 'mobile-development',
      displayName: 'Mobile Development',
      description: 'iOS, Android, Flutter, and React Native development news',
      icon: Icons.phone_android,
      category: TopicCategory.development,
    ),
    'web-development': TopicInfo(
      id: 'web-development',
      displayName: 'Web Development',
      description: 'Frontend, backend, frameworks, and web technologies',
      icon: Icons.web,
      category: TopicCategory.development,
    ),
    'cybersecurity': TopicInfo(
      id: 'cybersecurity',
      displayName: 'Cybersecurity',
      description: 'Security breaches, vulnerabilities, and protection strategies',
      icon: Icons.security,
      category: TopicCategory.security,
    ),
    'cloud-computing': TopicInfo(
      id: 'cloud-computing',
      displayName: 'Cloud Computing',
      description: 'AWS, Azure, Google Cloud, and cloud-native technologies',
      icon: Icons.cloud,
      category: TopicCategory.infrastructure,
    ),
    'gaming': TopicInfo(
      id: 'gaming',
      displayName: 'Gaming',
      description: 'Video games, game development, and gaming industry news',
      icon: Icons.sports_esports,
      category: TopicCategory.entertainment,
    ),
    'hardware': TopicInfo(
      id: 'hardware',
      displayName: 'Hardware',
      description: 'Computer hardware, processors, graphics cards, and devices',
      icon: Icons.memory,
      category: TopicCategory.technology,
    ),
    'science': TopicInfo(
      id: 'science',
      displayName: 'Science',
      description: 'Scientific discoveries, research, and technology breakthroughs',
      icon: Icons.science,
      category: TopicCategory.science,
    ),
  };

  // Get user's subscribed topics
  Future<Set<String>> getSubscribedTopics() async {
    final prefs = await SharedPreferences.getInstance();
    final topics = prefs.getStringList('subscribed_topics') ?? [];
    return topics.toSet();
  }

  // Subscribe to a topic
  Future<bool> subscribeToTopic(String topicId) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topicId);
      
      // Update local storage
      final prefs = await SharedPreferences.getInstance();
      final currentTopics = prefs.getStringList('subscribed_topics') ?? [];
      if (!currentTopics.contains(topicId)) {
        currentTopics.add(topicId);
        await prefs.setStringList('subscribed_topics', currentTopics);
      }
      
      // Log subscription
      await _logSubscriptionEvent(topicId, true);
      
      debugPrint('Successfully subscribed to topic: $topicId');
      return true;
    } catch (e) {
      debugPrint('Error subscribing to topic $topicId: $e');
      return false;
    }
  }

  // Unsubscribe from a topic
  Future<bool> unsubscribeFromTopic(String topicId) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topicId);
      
      // Update local storage
      final prefs = await SharedPreferences.getInstance();
      final currentTopics = prefs.getStringList('subscribed_topics') ?? [];
      currentTopics.remove(topicId);
      await prefs.setStringList('subscribed_topics', currentTopics);
      
      // Log unsubscription
      await _logSubscriptionEvent(topicId, false);
      
      debugPrint('Successfully unsubscribed from topic: $topicId');
      return true;
    } catch (e) {
      debugPrint('Error unsubscribing from topic $topicId: $e');
      return false;
    }
  }

  // Subscribe to multiple topics
  Future<Map<String, bool>> subscribeToMultipleTopics(List<String> topicIds) async {
    final results = <String, bool>{};
    
    for (final topicId in topicIds) {
      results[topicId] = await subscribeToTopic(topicId);
    }
    
    return results;
  }

  // Unsubscribe from multiple topics
  Future<Map<String, bool>> unsubscribeFromMultipleTopics(List<String> topicIds) async {
    final results = <String, bool>{};
    
    for (final topicId in topicIds) {
      results[topicId] = await unsubscribeFromTopic(topicId);
    }
    
    return results;
  }

  // Update subscriptions based on user preferences
  Future<void> updateSubscriptions(Set<String> newTopics) async {
    final currentTopics = await getSubscribedTopics();
    
    // Topics to subscribe to
    final toSubscribe = newTopics.difference(currentTopics);
    
    // Topics to unsubscribe from
    final toUnsubscribe = currentTopics.difference(newTopics);
    
    // Perform subscriptions
    if (toSubscribe.isNotEmpty) {
      await subscribeToMultipleTopics(toSubscribe.toList());
    }
    
    // Perform unsubscriptions
    if (toUnsubscribe.isNotEmpty) {
      await unsubscribeFromMultipleTopics(toUnsubscribe.toList());
    }
  }

  // Get topics by category
  Map<TopicCategory, List<TopicInfo>> getTopicsByCategory() {
    final categorizedTopics = <TopicCategory, List<TopicInfo>>{};
    
    for (final topic in availableTopics.values) {
      categorizedTopics.putIfAbsent(topic.category, () => []).add(topic);
    }
    
    return categorizedTopics;
  }

  // Get recommended topics based on user behavior
  Future<List<String>> getRecommendedTopics() async {
    // TODO: Implement ML-based recommendations
    // For now, return popular topics
    return [
      'artificial-intelligence',
      'startups',
      'mobile-development',
    ];
  }

  // Log subscription events for analytics
  Future<void> _logSubscriptionEvent(String topicId, bool subscribed) async {
    final prefs = await SharedPreferences.getInstance();
    final events = prefs.getStringList('subscription_events') ?? [];
    
    final event = {
      'topic_id': topicId,
      'subscribed': subscribed,
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    events.add(jsonEncode(event));
    
    // Keep only last 100 events
    if (events.length > 100) {
      events.removeRange(0, events.length - 100);
    }
    
    await prefs.setStringList('subscription_events', events);
  }

  // Get subscription statistics
  Future<Map<String, dynamic>> getSubscriptionStats() async {
    final subscribedTopics = await getSubscribedTopics();
    final prefs = await SharedPreferences.getInstance();
    final events = prefs.getStringList('subscription_events') ?? [];
    
    return {
      'total_subscribed': subscribedTopics.length,
      'total_available': availableTopics.length,
      'subscription_rate': subscribedTopics.length / availableTopics.length,
      'recent_events': events.length,
      'subscribed_topics': subscribedTopics.toList(),
      'categories_subscribed': _getCategoriesSubscribed(subscribedTopics),
    };
  }

  Set<TopicCategory> _getCategoriesSubscribed(Set<String> subscribedTopics) {
    final categories = <TopicCategory>{};
    
    for (final topicId in subscribedTopics) {
      final topic = availableTopics[topicId];
      if (topic != null) {
        categories.add(topic.category);
      }
    }
    
    return categories;
  }

  // Sync subscriptions with server (for backup/restore)
  Future<void> syncSubscriptionsWithServer() async {
    // TODO: Implement server sync
    final subscribedTopics = await getSubscribedTopics();
    debugPrint('Would sync topics with server: $subscribedTopics');
  }

  // Restore subscriptions from server
  Future<void> restoreSubscriptionsFromServer() async {
    // TODO: Implement server restore
    debugPrint('Would restore topics from server');
  }
}

// Topic information model
class TopicInfo {
  final String id;
  final String displayName;
  final String description;
  final IconData icon;
  final TopicCategory category;

  const TopicInfo({
    required this.id,
    required this.displayName,
    required this.description,
    required this.icon,
    required this.category,
  });
}

// Topic categories
enum TopicCategory {
  technology,
  business,
  finance,
  development,
  security,
  infrastructure,
  entertainment,
  science,
}

extension TopicCategoryExtension on TopicCategory {
  String get displayName {
    switch (this) {
      case TopicCategory.technology:
        return 'Technology';
      case TopicCategory.business:
        return 'Business';
      case TopicCategory.finance:
        return 'Finance';
      case TopicCategory.development:
        return 'Development';
      case TopicCategory.security:
        return 'Security';
      case TopicCategory.infrastructure:
        return 'Infrastructure';
      case TopicCategory.entertainment:
        return 'Entertainment';
      case TopicCategory.science:
        return 'Science';
    }
  }

  IconData get icon {
    switch (this) {
      case TopicCategory.technology:
        return Icons.computer;
      case TopicCategory.business:
        return Icons.business;
      case TopicCategory.finance:
        return Icons.attach_money;
      case TopicCategory.development:
        return Icons.code;
      case TopicCategory.security:
        return Icons.shield;
      case TopicCategory.infrastructure:
        return Icons.cloud_queue;
      case TopicCategory.entertainment:
        return Icons.games;
      case TopicCategory.science:
        return Icons.biotech;
    }
  }
}
