import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'dart:typed_data';
import 'notification_handler.dart';

class NotificationTemplates {
  static const String _channelBreakingNews = 'breaking_news';
  static const String _channelDailyDigest = 'daily_digest';
  static const String _channelTrendingArticles = 'trending_articles';
  static const String _channelGeneral = 'general';

  // Breaking News Template - High Priority with Rich Content
  static AndroidNotificationDetails createBreakingNewsNotification({
    required String title,
    required String body,
    String? imageUrl,
    String? articleUrl,
  }) {
    return AndroidNotificationDetails(
      _channelBreakingNews,
      'Breaking News',
      channelDescription: 'Urgent tech news and breaking stories',
      importance: Importance.max,
      priority: Priority.high,
      color: const Color(0xFFFF4444), // Red for urgency
      icon: '@drawable/ic_notification',
      largeIcon: const DrawableResourceAndroidBitmap('@drawable/ic_notification'),
      styleInformation: BigTextStyleInformation(
        body,
        htmlFormatBigText: true,
        contentTitle: title,
        htmlFormatContentTitle: true,
        summaryText: 'Breaking Tech News',
        htmlFormatSummaryText: true,
      ),
      enableLights: false, // Disabled to avoid LED timing issues on older Android
      enableVibration: true,
      vibrationPattern: Int64List.fromList([0, 1000, 500, 1000]), // Urgent pattern
      actions: [
        const AndroidNotificationAction(
          'read_now',
          'Read Now',
          icon: DrawableResourceAndroidBitmap('@drawable/ic_notification'),
          showsUserInterface: true,
        ),
        const AndroidNotificationAction(
          'save_later',
          'Save for Later',
          icon: DrawableResourceAndroidBitmap('@drawable/ic_notification'),
        ),
        const AndroidNotificationAction(
          'share',
          'Share',
          icon: DrawableResourceAndroidBitmap('@drawable/ic_notification'),
        ),
      ],
      category: AndroidNotificationCategory.recommendation,
      visibility: NotificationVisibility.public,
      autoCancel: true,
      ongoing: false,
      silent: false,
      when: DateTime.now().millisecondsSinceEpoch,
      showWhen: true,
      usesChronometer: false,
      chronometerCountDown: false,
      channelShowBadge: true,
      onlyAlertOnce: false,
      ticker: 'Breaking: $title',
    );
  }

  // Daily Digest Template - Rich Inbox Style
  static AndroidNotificationDetails createDailyDigestNotification({
    required String title,
    required List<String> articles,
    String? imageUrl,
  }) {
    return AndroidNotificationDetails(
      _channelDailyDigest,
      'Daily Digest',
      channelDescription: 'Daily summary of tech news',
      importance: Importance.defaultImportance,
      priority: Priority.defaultPriority,
      color: const Color(0xFF1E3A8A),
      icon: '@drawable/ic_notification',
      largeIcon: const DrawableResourceAndroidBitmap('@drawable/ic_notification'),
      styleInformation: InboxStyleInformation(
        articles.take(5).toList(), // Show up to 5 articles
        htmlFormatLines: true,
        contentTitle: title,
        htmlFormatContentTitle: true,
        summaryText: '${articles.length} articles today',
        htmlFormatSummaryText: true,
      ),
      enableLights: false,
      enableVibration: true,
      vibrationPattern: Int64List.fromList([0, 300, 200, 300]), // Gentle pattern
      actions: [
        const AndroidNotificationAction(
          'read_digest',
          'Read Digest',
          icon: DrawableResourceAndroidBitmap('@drawable/ic_notification'),
          showsUserInterface: true,
        ),
        const AndroidNotificationAction(
          'customize',
          'Customize',
          icon: DrawableResourceAndroidBitmap('@drawable/ic_notification'),
        ),
      ],
      category: AndroidNotificationCategory.recommendation,
      visibility: NotificationVisibility.public,
      autoCancel: true,
      ongoing: false,
      silent: false,
      when: DateTime.now().millisecondsSinceEpoch,
      showWhen: true,
      channelShowBadge: true,
      ticker: 'Daily Tech Digest Ready',
    );
  }

  // Trending Article Template - Image-focused
  static AndroidNotificationDetails createTrendingArticleNotification({
    required String title,
    required String body,
    required String imageUrl,
    String? author,
    int? readTime,
  }) {
    final summaryText = [
      if (author != null) 'by $author',
      if (readTime != null) '${readTime}min read',
    ].join(' • ');

    return AndroidNotificationDetails(
      _channelTrendingArticles,
      'Trending Articles',
      channelDescription: 'Popular and trending tech articles',
      importance: Importance.low,
      priority: Priority.low,
      color: const Color(0xFF1E3A8A),
      icon: '@drawable/ic_notification',
      largeIcon: const DrawableResourceAndroidBitmap('@drawable/ic_notification'),
      styleInformation: BigTextStyleInformation(
        body,
        contentTitle: title,
        htmlFormatContentTitle: true,
        summaryText: summaryText,
        htmlFormatSummaryText: true,
      ),
      enableLights: false,
      enableVibration: false,
      actions: [
        const AndroidNotificationAction(
          'read_article',
          'Read',
          icon: DrawableResourceAndroidBitmap('@drawable/ic_notification'),
          showsUserInterface: true,
        ),
        const AndroidNotificationAction(
          'bookmark',
          'Bookmark',
          icon: DrawableResourceAndroidBitmap('@drawable/ic_notification'),
        ),
        const AndroidNotificationAction(
          'share',
          'Share',
          icon: DrawableResourceAndroidBitmap('@drawable/ic_notification'),
        ),
      ],
      category: AndroidNotificationCategory.recommendation,
      visibility: NotificationVisibility.public,
      autoCancel: true,
      ongoing: false,
      silent: true, // Low priority, silent
      when: DateTime.now().millisecondsSinceEpoch,
      showWhen: true,
      channelShowBadge: false, // Don't show badge for trending
      ticker: 'Trending: $title',
    );
  }

  // Saved Article Update Template
  static AndroidNotificationDetails createSavedArticleUpdateNotification({
    required String title,
    required String body,
    required String originalTitle,
    String? imageUrl,
  }) {
    return AndroidNotificationDetails(
      _channelGeneral,
      'Article Updates',
      channelDescription: 'Updates on your saved articles',
      importance: Importance.defaultImportance,
      priority: Priority.defaultPriority,
      color: const Color(0xFF1E3A8A),
      icon: '@drawable/ic_notification',
      largeIcon: const DrawableResourceAndroidBitmap('@drawable/ic_notification'),
      styleInformation: BigTextStyleInformation(
        body,
        htmlFormatBigText: true,
        contentTitle: title,
        htmlFormatContentTitle: true,
        summaryText: 'Update on: $originalTitle',
        htmlFormatSummaryText: true,
      ),
      enableLights: false, // Disabled to avoid LED timing issues on older Android
      enableVibration: true,
      vibrationPattern: Int64List.fromList([0, 200, 100, 200]),
      actions: [
        const AndroidNotificationAction(
          'view_update',
          'View Update',
          icon: DrawableResourceAndroidBitmap('@drawable/ic_notification'),
          showsUserInterface: true,
        ),
        const AndroidNotificationAction(
          'view_original',
          'View Original',
          icon: DrawableResourceAndroidBitmap('@drawable/ic_notification'),
          showsUserInterface: true,
        ),
      ],
      category: AndroidNotificationCategory.status,
      visibility: NotificationVisibility.public,
      autoCancel: true,
      ongoing: false,
      when: DateTime.now().millisecondsSinceEpoch,
      showWhen: true,
      channelShowBadge: true,
      ticker: 'Article Update: $originalTitle',
    );
  }

  // General Template - Simple and Clean
  static AndroidNotificationDetails createGeneralNotification({
    required String title,
    required String body,
    String? imageUrl,
  }) {
    return AndroidNotificationDetails(
      _channelGeneral,
      'General Notifications',
      channelDescription: 'General app notifications',
      importance: Importance.defaultImportance,
      priority: Priority.defaultPriority,
      color: const Color(0xFF1E3A8A),
      icon: '@drawable/ic_notification',
      largeIcon: const DrawableResourceAndroidBitmap('@drawable/ic_notification'),
      styleInformation: DefaultStyleInformation(
        true, // HTML formatting
        true, // HTML formatting for title
      ),
      enableLights: false, // Disabled to avoid LED timing issues on older Android
      enableVibration: true,
      actions: [
        const AndroidNotificationAction(
          'open_app',
          'Open App',
          icon: DrawableResourceAndroidBitmap('@drawable/ic_notification'),
          showsUserInterface: true,
        ),
      ],
      category: AndroidNotificationCategory.recommendation,
      visibility: NotificationVisibility.public,
      autoCancel: true,
      ongoing: false,
      when: DateTime.now().millisecondsSinceEpoch,
      showWhen: true,
      channelShowBadge: true,
      ticker: title,
    );
  }

  // Get appropriate template based on notification type
  static AndroidNotificationDetails getTemplateForType({
    required NotificationType type,
    required String title,
    required String body,
    String? imageUrl,
    Map<String, dynamic>? extraData,
  }) {
    switch (type) {
      case NotificationType.breakingNews:
        return createBreakingNewsNotification(
          title: title,
          body: body,
          imageUrl: imageUrl,
          articleUrl: extraData?['article_url'],
        );
      
      case NotificationType.dailyDigest:
        final articles = (extraData?['articles'] as List<dynamic>?)
            ?.cast<String>() ?? [body];
        return createDailyDigestNotification(
          title: title,
          articles: articles,
          imageUrl: imageUrl,
        );
      
      case NotificationType.trendingArticle:
        return createTrendingArticleNotification(
          title: title,
          body: body,
          imageUrl: imageUrl ?? '',
          author: extraData?['author'],
          readTime: extraData?['read_time'],
        );
      
      case NotificationType.savedArticleUpdate:
        return createSavedArticleUpdateNotification(
          title: title,
          body: body,
          originalTitle: extraData?['original_title'] ?? 'Saved Article',
          imageUrl: imageUrl,
        );
      
      case NotificationType.general:
        return createGeneralNotification(
          title: title,
          body: body,
          imageUrl: imageUrl,
        );
    }
  }
}
