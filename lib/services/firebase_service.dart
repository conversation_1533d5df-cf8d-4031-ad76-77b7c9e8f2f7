import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'permission_service.dart';
import 'token_service.dart';
import 'notification_handler.dart';

class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final PermissionService _permissionService = PermissionService();
  final TokenService _tokenService = TokenService();
  final NotificationHandler _notificationHandler = NotificationHandler();

  String? get fcmToken => _tokenService.currentToken;

  // Initialize Firebase messaging
  Future<void> initialize() async {
    // Initialize notification handler
    await _notificationHandler.initialize();

    // Initialize token service
    await _tokenService.initialize();

    // Set up message handlers
    _setupMessageHandlers();
  }

  // Request notification permissions with user-friendly UI
  Future<NotificationPermissionStatus> requestPermission(BuildContext context) async {
    return await _permissionService.requestNotificationPermission(context: context);
  }

  // Check current permission status
  Future<NotificationPermissionStatus> getPermissionStatus() async {
    return await _permissionService.getNotificationPermissionStatus();
  }

  // Set up message handlers
  void _setupMessageHandlers() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      debugPrint('=== FOREGROUND MESSAGE RECEIVED ===');
      debugPrint('Message ID: ${message.messageId}');
      debugPrint('Message data: ${message.data}');
      debugPrint('Notification title: ${message.notification?.title}');
      debugPrint('Notification body: ${message.notification?.body}');
      debugPrint('Android notification: ${message.notification?.android?.toMap()}');
      debugPrint('From: ${message.from}');
      debugPrint('Category: ${message.category}');
      debugPrint('Collapse Key: ${message.collapseKey}');
      debugPrint('Content Available: ${message.contentAvailable}');
      debugPrint('Message Type: ${message.messageType}');
      debugPrint('Sent Time: ${message.sentTime}');
      debugPrint('TTL: ${message.ttl}');
      debugPrint('=== END MESSAGE DEBUG ===');

      // Use notification handler to process the message
      _notificationHandler.handleNotification(message);
    });

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint('A new onMessageOpenedApp event was published!');
      // Use notification handler to process the tap
      _notificationHandler.handleNotification(message);
    });
  }

  // Get token statistics
  Future<Map<String, dynamic>> getTokenStats() async {
    return await _tokenService.getTokenStats();
  }

  // Check if token is valid
  Future<bool> isTokenValid() async {
    return await _tokenService.isTokenValid();
  }

  // Force refresh token
  Future<String?> forceRefreshToken() async {
    return await _tokenService.forceRefreshToken();
  }

  // Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      debugPrint('Subscribed to topic: $topic');
    } catch (e) {
      debugPrint('Error subscribing to topic $topic: $e');
    }
  }

  // Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      debugPrint('Unsubscribed from topic: $topic');
    } catch (e) {
      debugPrint('Error unsubscribing from topic $topic: $e');
    }
  }

  // Get saved FCM token
  Future<String?> getSavedToken() async {
    return _tokenService.currentToken;
  }

  // Get notification history
  List<NotificationData> getNotificationHistory() {
    return _notificationHandler.notificationHistory;
  }

  // Clear notification history
  Future<void> clearNotificationHistory() async {
    await _notificationHandler.clearHistory();
  }
}
