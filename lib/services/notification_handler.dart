import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'notification_templates.dart';
import 'dart:convert';

enum NotificationType {
  breakingNews,
  dailyDigest,
  trendingArticle,
  savedArticleUpdate,
  general,
}

class NotificationData {
  final String id;
  final NotificationType type;
  final String title;
  final String body;
  final String? imageUrl;
  final String? articleUrl;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  NotificationData({
    required this.id,
    required this.type,
    required this.title,
    required this.body,
    this.imageUrl,
    this.articleUrl,
    required this.data,
    required this.timestamp,
  });

  factory NotificationData.fromRemoteMessage(RemoteMessage message) {
    final data = message.data;
    
    return NotificationData(
      id: message.messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
      type: _parseNotificationType(data['type']),
      title: message.notification?.title ?? data['title'] ?? 'Tech News',
      body: message.notification?.body ?? data['body'] ?? 'New update available',
      imageUrl: message.notification?.android?.imageUrl ?? data['image_url'],
      articleUrl: data['article_url'],
      data: data,
      timestamp: DateTime.now(),
    );
  }

  static NotificationType _parseNotificationType(String? typeString) {
    switch (typeString?.toLowerCase()) {
      case 'breaking_news':
        return NotificationType.breakingNews;
      case 'daily_digest':
        return NotificationType.dailyDigest;
      case 'trending_article':
        return NotificationType.trendingArticle;
      case 'saved_article_update':
        return NotificationType.savedArticleUpdate;
      default:
        return NotificationType.general;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'title': title,
      'body': body,
      'imageUrl': imageUrl,
      'articleUrl': articleUrl,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

class NotificationHandler {
  static final NotificationHandler _instance = NotificationHandler._internal();
  factory NotificationHandler() => _instance;
  NotificationHandler._internal();

  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  final List<NotificationData> _notificationHistory = [];

  // Initialize notification handler
  Future<void> initialize() async {
    await _initializeLocalNotifications();
    await _loadNotificationHistory();
  }

  // Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@drawable/ic_notification');

    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channels
    await _createNotificationChannels();
  }

  // Create different notification channels for different types
  Future<void> _createNotificationChannels() async {
    final androidPlugin = _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();

    if (androidPlugin == null) return;

    // Breaking News Channel (High Priority)
    const breakingNewsChannel = AndroidNotificationChannel(
      'breaking_news',
      'Breaking News',
      description: 'Urgent tech news and breaking stories',
      importance: Importance.max,
      enableLights: false, // Disabled to avoid LED timing issues on older Android
      enableVibration: true,
    );

    // Daily Digest Channel (Normal Priority)
    const dailyDigestChannel = AndroidNotificationChannel(
      'daily_digest',
      'Daily Digest',
      description: 'Daily summary of tech news',
      importance: Importance.defaultImportance,
      enableLights: false,
      enableVibration: false,
    );

    // Trending Articles Channel (Low Priority)
    const trendingChannel = AndroidNotificationChannel(
      'trending_articles',
      'Trending Articles',
      description: 'Popular and trending tech articles',
      importance: Importance.low,
      enableLights: false,
      enableVibration: false,
    );

    // General Channel
    const generalChannel = AndroidNotificationChannel(
      'general',
      'General Notifications',
      description: 'General app notifications',
      importance: Importance.defaultImportance,
    );

    await androidPlugin.createNotificationChannel(breakingNewsChannel);
    await androidPlugin.createNotificationChannel(dailyDigestChannel);
    await androidPlugin.createNotificationChannel(trendingChannel);
    await androidPlugin.createNotificationChannel(generalChannel);
  }

  // Handle incoming notification
  Future<void> handleNotification(RemoteMessage message) async {
    final notificationData = NotificationData.fromRemoteMessage(message);
    
    // Check if user wants this type of notification
    if (!await _shouldShowNotification(notificationData.type)) {
      debugPrint('Notification filtered out: ${notificationData.type}');
      return;
    }

    // Check quiet hours
    if (await _isInQuietHours()) {
      debugPrint('Notification suppressed due to quiet hours');
      await _saveForLater(notificationData);
      return;
    }

    // Show notification
    await _showLocalNotification(notificationData);
    
    // Save to history
    await _saveToHistory(notificationData);
  }

  // Show local notification with appropriate styling
  Future<void> _showLocalNotification(NotificationData notificationData) async {
    final channelId = _getChannelId(notificationData.type);
    final notificationDetails = await _buildNotificationDetails(notificationData, channelId);

    await _localNotifications.show(
      notificationData.id.hashCode,
      notificationData.title,
      notificationData.body,
      notificationDetails,
      payload: jsonEncode(notificationData.toJson()),
    );
  }

  // Build notification details based on type using rich templates
  Future<NotificationDetails> _buildNotificationDetails(
    NotificationData notificationData,
    String channelId,
  ) async {
    final androidDetails = NotificationTemplates.getTemplateForType(
      type: notificationData.type,
      title: notificationData.title,
      body: notificationData.body,
      imageUrl: notificationData.imageUrl,
      extraData: notificationData.data,
    );

    return NotificationDetails(android: androidDetails);
  }



  // Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    try {
      final payload = response.payload;
      if (payload == null) return;

      final notificationData = jsonDecode(payload) as Map<String, dynamic>;
      final actionId = response.actionId;

      debugPrint('Notification tapped: $actionId, data: $notificationData');

      // Handle different actions
      switch (actionId) {
        case 'read_action':
          _handleReadAction(notificationData);
          break;
        case 'save_action':
          _handleSaveAction(notificationData);
          break;
        default:
          _handleDefaultTap(notificationData);
          break;
      }
    } catch (e) {
      debugPrint('Error handling notification tap: $e');
    }
  }

  // Handle read action
  void _handleReadAction(Map<String, dynamic> data) {
    final articleUrl = data['articleUrl'] as String?;
    if (articleUrl != null) {
      // TODO: Navigate to article
      debugPrint('Opening article: $articleUrl');
    }
  }

  // Handle save action
  void _handleSaveAction(Map<String, dynamic> data) {
    // TODO: Save article to bookmarks
    debugPrint('Saving article: ${data['title']}');
  }

  // Handle default tap
  void _handleDefaultTap(Map<String, dynamic> data) {
    // TODO: Navigate to appropriate screen
    debugPrint('Default tap: ${data['type']}');
  }

  // Check if notification should be shown based on user preferences
  Future<bool> _shouldShowNotification(NotificationType type) async {
    final prefs = await SharedPreferences.getInstance();
    
    switch (type) {
      case NotificationType.breakingNews:
        return prefs.getBool('pref_breaking_news') ?? true;
      case NotificationType.dailyDigest:
        return prefs.getBool('pref_daily_digest') ?? true;
      case NotificationType.trendingArticle:
        return prefs.getBool('pref_trending_articles') ?? true;
      case NotificationType.savedArticleUpdate:
        return prefs.getBool('pref_saved_updates') ?? true;
      case NotificationType.general:
        return true;
    }
  }

  // Check if current time is in quiet hours
  Future<bool> _isInQuietHours() async {
    final prefs = await SharedPreferences.getInstance();
    final quietHoursEnabled = prefs.getBool('pref_quiet_hours_enabled') ?? false;
    
    if (!quietHoursEnabled) return false;

    final startTime = prefs.getString('pref_quiet_hours_start') ?? '22:00';
    final endTime = prefs.getString('pref_quiet_hours_end') ?? '07:00';
    
    final now = TimeOfDay.now();
    final start = _parseTimeOfDay(startTime);
    final end = _parseTimeOfDay(endTime);
    
    // Handle overnight quiet hours (e.g., 22:00 to 07:00)
    if (start.hour > end.hour) {
      return now.hour >= start.hour || now.hour < end.hour;
    } else {
      return now.hour >= start.hour && now.hour < end.hour;
    }
  }

  TimeOfDay _parseTimeOfDay(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
  }

  // Save notification for later (during quiet hours)
  Future<void> _saveForLater(NotificationData notificationData) async {
    final prefs = await SharedPreferences.getInstance();
    final savedNotifications = prefs.getStringList('saved_notifications') ?? [];
    savedNotifications.add(jsonEncode(notificationData.toJson()));
    await prefs.setStringList('saved_notifications', savedNotifications);
  }

  // Save notification to history
  Future<void> _saveToHistory(NotificationData notificationData) async {
    _notificationHistory.insert(0, notificationData);
    
    // Keep only last 100 notifications
    if (_notificationHistory.length > 100) {
      _notificationHistory.removeRange(100, _notificationHistory.length);
    }
    
    await _persistNotificationHistory();
  }

  // Load notification history
  Future<void> _loadNotificationHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = prefs.getStringList('notification_history') ?? [];
    
    _notificationHistory.clear();
    for (final json in historyJson) {
      try {
        final data = jsonDecode(json) as Map<String, dynamic>;
        // TODO: Create NotificationData.fromJson constructor
        debugPrint('Loaded notification: ${data['title']}');
      } catch (e) {
        debugPrint('Error loading notification from history: $e');
      }
    }
  }

  // Persist notification history
  Future<void> _persistNotificationHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = _notificationHistory
        .map((notification) => jsonEncode(notification.toJson()))
        .toList();
    await prefs.setStringList('notification_history', historyJson);
  }

  // Helper method for channel configuration
  String _getChannelId(NotificationType type) {
    switch (type) {
      case NotificationType.breakingNews:
        return 'breaking_news';
      case NotificationType.dailyDigest:
        return 'daily_digest';
      case NotificationType.trendingArticle:
        return 'trending_articles';
      default:
        return 'general';
    }
  }

  // Get notification history
  List<NotificationData> get notificationHistory => List.unmodifiable(_notificationHistory);

  // Clear notification history
  Future<void> clearHistory() async {
    _notificationHistory.clear();
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('notification_history');
  }
}
