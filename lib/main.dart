
// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'services/firebase_service.dart';
import 'services/permission_service.dart';
import 'services/notification_handler.dart';
import 'screens/notification_settings_screen.dart';
import 'dart:io';

// Global function to handle background messages
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  debugPrint('Handling a background message: ${message.messageId}');

  // Process the notification in the background
  try {
    final notificationHandler = NotificationHandler();
    await notificationHandler.handleNotification(message);
  } catch (e) {
    debugPrint('Error handling background notification: $e');
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp();

  // Set up background message handler
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // Initialize Firebase service
  await FirebaseService().initialize();

  runApp(const TechNewsApp());
}

class TechNewsApp extends StatelessWidget {
  const TechNewsApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Tech News',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        primaryColor: const Color(0xFF1E3A8A), // Tech news blue
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF1E3A8A),
          foregroundColor: Colors.white,
          elevation: 2,
        ),
      ),
      home: const TechNewsWebView(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class TechNewsWebView extends StatefulWidget {
  const TechNewsWebView({super.key});

  @override
  State<TechNewsWebView> createState() => _TechNewsWebViewState();
}

class _TechNewsWebViewState extends State<TechNewsWebView> {
  late final WebViewController controller;
  int loadingProgress = 0;
  bool isLoading = true;
  bool canGoBack = false;
  bool canGoForward = false;
  String? errorMessage;
  bool hasError = false;
  bool isRefreshing = false;
  double _pullDistance = 0.0;
  bool _isPulling = false;
  final double _pullThreshold = 80.0;
  bool _hasRequestedPermissions = false;

  Future<String> _loadLocalHtml() async {
    return await rootBundle.loadString('assets/error_fallback.html');
  }

  Future<void> _handleRefresh() async {
    try {
      setState(() {
        isRefreshing = true;
        errorMessage = null;
        hasError = false;
      });

      // Add a small delay to show the refresh indicator
      await Future.delayed(const Duration(milliseconds: 300));

      // Reload the WebView
      await controller.reload();

      // Show success feedback
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white, size: 20),
                SizedBox(width: 8),
                Text('Page refreshed successfully'),
              ],
            ),
            backgroundColor: Color(0xFF1E3A8A),
            duration: Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      // Handle refresh errors
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Expanded(child: Text('Refresh failed: ${e.toString()}')),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isRefreshing = false;
        });
      }
    }
  }

  void _onPanStart(DragStartDetails details) {
    // Only start tracking if we're not already refreshing
    if (!isRefreshing) {
      setState(() {
        _isPulling = false;
        _pullDistance = 0.0;
      });
    }
  }

  void _onPanUpdate(DragUpdateDetails details) {
    // Only track downward pulls when not refreshing
    if (!isRefreshing && details.delta.dy > 0) {
      setState(() {
        _isPulling = true;
        _pullDistance += details.delta.dy;
        // Limit the pull distance to prevent excessive pulling
        _pullDistance = _pullDistance.clamp(0.0, 120.0);
      });
    }
  }

  void _onPanEnd(DragEndDetails details) {
    // Trigger refresh if pulled down enough
    if (_isPulling && _pullDistance > _pullThreshold && !isRefreshing) {
      _handleRefresh();
    }

    // Reset pull state
    setState(() {
      _isPulling = false;
      _pullDistance = 0.0;
    });
  }

  // Request notification permissions after app loads
  Future<void> _requestNotificationPermissions() async {
    if (_hasRequestedPermissions) return;

    // Wait a bit for the app to fully load
    await Future.delayed(const Duration(seconds: 2));

    if (!mounted) return;

    final firebaseService = FirebaseService();
    final permissionStatus = await firebaseService.getPermissionStatus();

    // Only request if not already granted or denied
    if (permissionStatus == NotificationPermissionStatus.notDetermined && mounted) {
      await firebaseService.requestPermission(context);
    }

    setState(() {
      _hasRequestedPermissions = true;
    });
  }



  @override
  void initState() {
    super.initState();

    // Configure Android WebView settings to help with ORB issues
    if (Platform.isAndroid) {
      AndroidWebViewController.enableDebugging(true);
    }

    // Configure WebView specifically for your tech-news.io site
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setUserAgent('Mozilla/5.0 (Linux; Android 15; Google Pixel 9) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 TechNewsApp/1.0')
      ..enableZoom(true)
      ..setBackgroundColor(Colors.white)
      ..setOnConsoleMessage((JavaScriptConsoleMessage consoleMessage) {
          debugPrint(
            '== JS == ${consoleMessage.level.name}: ${consoleMessage.message}' 
          );
      })
      ..addJavaScriptChannel(
        'TechNewsApp',
        onMessageReceived: (JavaScriptMessage message) {
          // Handle messages from your tech-news.io site
          debugPrint('Message from tech-news.io: ${message.message}');
        },
      )
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            setState(() {
              loadingProgress = progress;
            });
          },
          onPageStarted: (String url) {
            setState(() {
              isLoading = true;
              loadingProgress = 0;
              errorMessage = null; // Clear any previous errors
              hasError = false; // Clear error state
            });
          },
          onPageFinished: (String url) async {
            setState(() {
              isLoading = false;
              loadingProgress = 100;
            });
            // Update navigation state
            final canGoBackResult = await controller.canGoBack();
            final canGoForwardResult = await controller.canGoForward();
            setState(() {
              canGoBack = canGoBackResult;
              canGoForward = canGoForwardResult;
            });

            // Request notification permissions after page loads
            _requestNotificationPermissions();
          },
          onHttpError: (HttpResponseError error) {
            /*setState(() {
              errorMessage = 'HTTP Error ${error.response?.statusCode}: Failed to load content';
              isLoading = false;
            });*/
          },
          onWebResourceError: (WebResourceError error) async {
            /*setState(() {
              errorMessage = 'Network Error: ${error.description}';
              isLoading = false;
            });*/ 
            if (error.isForMainFrame!) {
              final fallbackHtml = await _loadLocalHtml();
              controller.loadHtmlString(fallbackHtml);
              setState(() => hasError = true);
            }
          },
          onNavigationRequest: (NavigationRequest request) {
            // Allow tech news sites and necessary resources
            final uri = Uri.parse(request.url);
            final host = uri.host.toLowerCase();

            // Allow major tech news sites
            final allowedDomains = [
              // Primary tech-news.io domain
              'tech-news.io',
              'www.tech-news.io',
              'cdn.tech-news.io',
              'assets.tech-news.io',
              'static.tech-news.io',
              'images.tech-news.io',
              'api.tech-news.io',

              // Major tech news sites
              'techcrunch.com',
              'www.techcrunch.com',
              'arstechnica.com',
              'www.arstechnica.com',
              'theverge.com',
              'www.theverge.com',
              'wired.com',
              'www.wired.com',
              'engadget.com',
              'www.engadget.com',
              'gizmodo.com',
              'www.gizmodo.com',
              'mashable.com',
              'www.mashable.com',
              'venturebeat.com',
              'www.venturebeat.com',
              'recode.net',
              'www.recode.net',

              // Common CDN and resource domains
              'wp.com',
              'wordpress.com',
              'gravatar.com',
              'secure.gravatar.com',
              's0.wp.com',
              's1.wp.com',
              's2.wp.com',
              's3.wp.com',
              'i0.wp.com',
              'i1.wp.com',
              'i2.wp.com',
              'cdn.vox-cdn.com',
              'duet-cdn.vox-cdn.com',
              'chorus-cdn.vox-cdn.com',
              'cdn.arstechnica.net',
              'cdn.mos.cms.futurecdn.net',
              'images.unsplash.com',
              'cdn.pixabay.com',
              'media.giphy.com',

              // Social media embeds (common in tech articles)
              'twitter.com',
              'www.twitter.com',
              'youtube.com',
              'www.youtube.com',
              'youtu.be',
              'instagram.com',
              'www.instagram.com',

              // Analytics and tracking (needed for proper site function)
              'google-analytics.com',
              'googletagmanager.com',
              'doubleclick.net',
              'googlesyndication.com',
              'googleadservices.com',
              'facebook.com',
              'connect.facebook.net',

              // Common web fonts and libraries
              'fonts.googleapis.com',
              'fonts.gstatic.com',
              'ajax.googleapis.com',
              'cdnjs.cloudflare.com',
              'unpkg.com',
              'jsdelivr.net'
            ];

            // Check if the host is in allowed domains or is a subdomain of allowed domains
            for (final domain in allowedDomains) {
              if (host == domain || host.endsWith('.$domain')) {
                return NavigationDecision.navigate;
              }
            }

            // Block navigation to external domains
            // return NavigationDecision.prevent;
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(
        Uri.parse('https://tech-news.io'),
       // Uri.parse('http://*************:3000'),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Linux; Android 13; Google Pixel 9) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 TechNewsApp/1.0',
          // 'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
          'Accept-Encoding': 'gzip, deflate, br',
          'Cache-Control': 'no-cache',
          // 'Content-Type': 'text/html; charset=utf-8',
          'Pragma': 'no-cache',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'none',
          'Sec-Fetch-User': '?1',
          'X-Client-Type' : 'mobile-app'
        },
      );
  }

  // #docregion webview_widget
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tech News'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const NotificationSettingsScreen(),
                ),
              );
            },
            tooltip: 'Notification Settings',
          ),
        ],
        bottom: loadingProgress < 100
            ? PreferredSize(
                preferredSize: const Size.fromHeight(4.0),
                child: LinearProgressIndicator(
                  value: loadingProgress / 100.0,
                  backgroundColor: Colors.grey[300],
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
              )
            : null,
      ),
      body: Stack(
        children: [
          GestureDetector(
            onPanStart: _onPanStart,
            onPanUpdate: _onPanUpdate,
            onPanEnd: _onPanEnd,
            child: Stack(
              children: [
                WebViewWidget(controller: controller),
                // Pull-to-refresh indicator
                if (_isPulling || isRefreshing)
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: _isPulling
                        ? (_pullDistance * 0.8).clamp(0.0, 80.0)
                        : (isRefreshing ? 60.0 : 0.0),
                      decoration: BoxDecoration(
                        color: const Color(0xFF1E3A8A).withValues(alpha: 0.9),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Center(
                        child: _isPulling && !isRefreshing
                          ? Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  _pullDistance > _pullThreshold
                                    ? Icons.refresh
                                    : Icons.keyboard_arrow_down,
                                  color: Colors.white,
                                  size: 24,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  _pullDistance > _pullThreshold
                                    ? 'Release to refresh'
                                    : 'Pull to refresh',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            )
                          : isRefreshing
                            ? const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                    ),
                                  ),
                                  SizedBox(width: 12),
                                  Text(
                                    'Refreshing...',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              )
                            : const SizedBox.shrink(),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          if (isLoading)
            Container(
              color: Colors.white.withValues(alpha: 0.8),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text(
                      'Loading Tech News...',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          if (errorMessage != null)
            Container(
              color: Colors.white.withValues(alpha: 0.95),
              child: Center(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Connection Error',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32),
                      child: Text(
                        errorMessage!,
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: isRefreshing ? null : _handleRefresh,
                      icon: isRefreshing
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Icon(Icons.refresh),
                      label: Text(isRefreshing ? 'Refreshing...' : 'Try Again'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF1E3A8A),
                        foregroundColor: Colors.white,
                      ),
                    ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
      bottomNavigationBar: Container(
        height: 60,
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: canGoBack
                  ? () async {
                      await controller.goBack();
                    }
                  : null,
              tooltip: 'Go Back',
            ),
            IconButton(
              icon: const Icon(Icons.arrow_forward),
              onPressed: canGoForward
                  ? () async {
                      await controller.goForward();
                    }
                  : null,
              tooltip: 'Go Forward',
            ),
            IconButton(
              icon: isRefreshing
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1E3A8A)),
                    ),
                  )
                : const Icon(Icons.refresh),
              onPressed: isRefreshing ? null : _handleRefresh,
              tooltip: 'Refresh',
            ),
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () async {
                await controller.loadRequest(
                  Uri.parse('https://tech-news.io'),
                  headers: {
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'DNT': '1',
                    'Connection': 'keep-alive',
                    'Upgrade-Insecure-Requests': '1',
                    'Sec-Fetch-Dest': 'document',
                    'Sec-Fetch-Mode': 'navigate',
                    'Sec-Fetch-Site': 'none',
                    'Cache-Control': 'max-age=0',
                  },
                );
              },
              tooltip: 'Home',
            ),
          ],
        ),
      ),
    );
  }
  // #enddocregion webview_widget
}
